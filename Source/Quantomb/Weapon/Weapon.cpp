// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.


#include "Weapon.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "GameFramework/PlayerController.h"
#include "Camera/PlayerCameraManager.h"
#include "GameFramework/Pawn.h"
#include "Quantomb/Player/QuantombPlayer.h"


// Sets default values
AWeapon::AWeapon()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AWeapon::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AWeapon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

// Gets transform of aim point from component of either type IronSights or Scope
AWeaponComponent* AWeapon::GetWeaponComponentByType(EWeaponComponentType WeaponComponentType)
{
	// Get all attached actors and filter for weapon components
	TArray<AActor*> AttachedActors;
	GetAttachedActors(AttachedActors);

	for (AActor* AttachedActor : AttachedActors)
	{
		if (AWeaponComponent* WeaponComponent = Cast<AWeaponComponent>(AttachedActor))
		{
			if (WeaponComponent->WeaponComponentType == WeaponComponentType)
			{
				return WeaponComponent;
			}
		}
	}
	return nullptr;
}

/**
 * Gets transform that the WeaponControl should be set to for ADS
 * @param WeaponComponentType The type of weapon component to get the aim point from (IronSights or Scope)
 * @return The transform the WeaponControl should be set to
 */
FTransform AWeapon::GetADSAimPointTransform(EWeaponComponentType WeaponComponentType)
{
	// Get the weapon component (iron sights or scope)
	AWeaponComponent* WeaponComponent = GetWeaponComponentByType(WeaponComponentType);
	if (!WeaponComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponComponent not found for type: %d"), (int32)WeaponComponentType);
		return FTransform::Identity;
	}

	// Get the skeletal mesh component from the weapon component
	USkeletalMeshComponent* SkeletalMeshComponent = WeaponComponent->FindComponentByClass<USkeletalMeshComponent>();
	if (!SkeletalMeshComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("SkeletalMeshComponent not found on WeaponComponent"));
		return FTransform::Identity;
	}

	// Get the transform of the "aimpoint" socket in world space
	const FTransform AimPointWorldTransform = SkeletalMeshComponent->GetSocketTransform(FName("aimpoint"), ERelativeTransformSpace::RTS_World);

	// Find the player controller
	UWorld* World = GetWorld();
	if (!World)
	{
		UE_LOG(LogTemp, Warning, TEXT("World not found"));
		return FTransform::Identity;
	}

	APlayerController* PlayerController = World->GetFirstPlayerController();
	if (!PlayerController)
	{
		UE_LOG(LogTemp, Warning, TEXT("PlayerController not found"));
		return FTransform::Identity;
	}

	// Get the player pawn
	APawn* PlayerPawn = PlayerController->GetPawn();
	if (!PlayerPawn)
	{
		UE_LOG(LogTemp, Warning, TEXT("PlayerPawn not found"));
		return FTransform::Identity;
	}

	// Cast to QuantombPlayer to access WeaponControl
	AQuantombPlayer* QuantombPlayer = Cast<AQuantombPlayer>(PlayerPawn);
	if (!QuantombPlayer)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to cast PlayerPawn to QuantombPlayer"));
		return FTransform::Identity;
	}

	// Find the WeaponControl static mesh component
	UStaticMeshComponent* WeaponControl = QuantombPlayer->FindComponentByClass<UStaticMeshComponent>();
	if (!WeaponControl)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponControl StaticMeshComponent not found"));
		return FTransform::Identity;
	}


	

	// Define a distance for the aim point (adjust as needed)
	const float AimDistance = 10.0f; // 10 meters forward
	
	// Get current WeaponControl world transform
	const FTransform WeaponControlWorldTransform = WeaponControl->GetComponentTransform();

	// Calculate the offset from WeaponControl to the aimpoint in the weapon's local space
	const FTransform WeaponControlToAimPoint = WeaponControlWorldTransform.GetRelativeTransform(AimPointWorldTransform);

	const FVector CameraCenter = FVector(0, 0, 0);

	// Calculate where WeaponControl needs to be so that the aimpoint aligns with the target
	// We need to position WeaponControl such that: WeaponControl + Offset = TargetAimLocation
	// Therefore: WeaponControl = TargetAimLocation - Offset
	const FVector TargetWeaponControlLocation = CameraCenter + WeaponControlToAimPoint.GetLocation();

	const FQuat NeutralQuat = FQuat(0, 0, 0, 0); 

	// Create the target transform for WeaponControl
	const FTransform TargetWeaponControlTransform(NeutralQuat, TargetWeaponControlLocation);

	// Convert to relative transform (relative to the player)
	/*const FTransform PlayerWorldTransform = QuantombPlayer->GetActorTransform();
	const FTransform RelativeWeaponControlTransform = TargetWeaponControlTransform.GetRelativeTransform(PlayerWorldTransform);*/

	// Store for reference
	CurrentADSAimPointTransform = TargetWeaponControlTransform;

	return TargetWeaponControlTransform;
}


